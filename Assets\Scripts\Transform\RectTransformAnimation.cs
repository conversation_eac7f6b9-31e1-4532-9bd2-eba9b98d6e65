using UnityEngine;
using Data.Global;

namespace Logic.Global
{
    public class RectTransformAnimation : TransformAnimationBase<RectTransform, RectTransformAnimationPreset>
    {
        private Vector2[] initialAnchoredPositions;
        private Vector2[] initialAnchorMins;
        private Vector2[] initialAnchorMaxs;
        private Vector2[] initialSizeDeltas;
        private Vector2[] initialPivots;
        private Quaternion[] initialRotations;
        private Vector3[] initialScales;

        private Vector2[] targetAnchoredPositions;
        private Vector2[] targetAnchorMins;
        private Vector2[] targetAnchorMaxs;
        private Vector2[] targetSizeDeltas;
        private Vector2[] targetPivots;
        private Quaternion[] targetRotations;
        private Vector3[] targetScales;

        protected override void ProcessAnimation(float deltaTime)
        {
            if (!isPlaying || currentPreset == null)
                return;

            currentTime += deltaTime;
            float t = Mathf.Clamp01(currentTime / currentPreset.duration);
            float curve = GetCurveValue(t);

            for (int i = 0; i < currentPreset.targets.Length; i++)
            {
                var target = currentPreset.targets[i];

                switch (target.rectTransformMode)
                {
                    case RectTransformMode.AnchoredPosition:
                        targetTransform.anchoredPosition = Vector2.Lerp(initialAnchoredPositions[i], targetAnchoredPositions[i], curve);
                        break;

                    case RectTransformMode.AnchorMin:
                        targetTransform.anchorMin = Vector2.Lerp(initialAnchorMins[i], targetAnchorMins[i], curve);
                        break;

                    case RectTransformMode.AnchorMax:
                        targetTransform.anchorMax = Vector2.Lerp(initialAnchorMaxs[i], targetAnchorMaxs[i], curve);
                        break;

                    case RectTransformMode.SizeDelta:
                        targetTransform.sizeDelta = Vector2.Lerp(initialSizeDeltas[i], targetSizeDeltas[i], curve);
                        break;

                    case RectTransformMode.Pivot:
                        targetTransform.pivot = Vector2.Lerp(initialPivots[i], targetPivots[i], curve);
                        break;

                    case RectTransformMode.Rotation:
                        targetTransform.localRotation = Quaternion.Lerp(initialRotations[i], targetRotations[i], curve);
                        break;

                    case RectTransformMode.Scale:
                        targetTransform.localScale = Vector3.Lerp(initialScales[i], targetScales[i], curve);
                        break;
                }
            }

            if (t >= 1f)
            {
                StartNextPreset();
            }
        }

        protected override void InitializeArrays()
        {
            int targetCount = currentPreset.targets.Length;

            initialAnchoredPositions = new Vector2[targetCount];
            initialAnchorMins = new Vector2[targetCount];
            initialAnchorMaxs = new Vector2[targetCount];
            initialSizeDeltas = new Vector2[targetCount];
            initialPivots = new Vector2[targetCount];
            initialRotations = new Quaternion[targetCount];
            initialScales = new Vector3[targetCount];

            targetAnchoredPositions = new Vector2[targetCount];
            targetAnchorMins = new Vector2[targetCount];
            targetAnchorMaxs = new Vector2[targetCount];
            targetSizeDeltas = new Vector2[targetCount];
            targetPivots = new Vector2[targetCount];
            targetRotations = new Quaternion[targetCount];
            targetScales = new Vector3[targetCount];

            for (int i = 0; i < targetCount; i++)
            {
                var target = currentPreset.targets[i];

                switch (target.rectTransformMode)
                {
                    case RectTransformMode.AnchoredPosition:
                        initialAnchoredPositions[i] = targetTransform.anchoredPosition;
                        targetAnchoredPositions[i] = currentPreset.incremental ?
                            initialAnchoredPositions[i] + target.vector2Value :
                            target.vector2Value;
                        break;

                    case RectTransformMode.AnchorMin:
                        initialAnchorMins[i] = targetTransform.anchorMin;
                        targetAnchorMins[i] = currentPreset.incremental ?
                            initialAnchorMins[i] + target.vector2Value :
                            target.vector2Value;
                        break;

                    case RectTransformMode.AnchorMax:
                        initialAnchorMaxs[i] = targetTransform.anchorMax;
                        targetAnchorMaxs[i] = currentPreset.incremental ?
                            initialAnchorMaxs[i] + target.vector2Value :
                            target.vector2Value;
                        break;

                    case RectTransformMode.SizeDelta:
                        initialSizeDeltas[i] = targetTransform.sizeDelta;
                        targetSizeDeltas[i] = currentPreset.incremental ?
                            initialSizeDeltas[i] + target.vector2Value :
                            target.vector2Value;
                        break;

                    case RectTransformMode.Pivot:
                        initialPivots[i] = targetTransform.pivot;
                        targetPivots[i] = currentPreset.incremental ?
                            initialPivots[i] + target.vector2Value :
                            target.vector2Value;
                        break;

                    case RectTransformMode.Rotation:
                        initialRotations[i] = targetTransform.localRotation;
                        targetRotations[i] = currentPreset.incremental ?
                            initialRotations[i] * Quaternion.Euler(target.vector3Value) :
                            Quaternion.Euler(target.vector3Value);
                        break;

                    case RectTransformMode.Scale:
                        initialScales[i] = targetTransform.localScale;
                        targetScales[i] = currentPreset.incremental ?
                            initialScales[i] + target.vector3Value :
                            target.vector3Value;
                        break;
                }
            }
        }
    }
}